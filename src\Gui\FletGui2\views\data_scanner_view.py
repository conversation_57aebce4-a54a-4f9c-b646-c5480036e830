# d:\infoTech\fip-rpa-robot-oldscriptref\src\Gui\FletGui2\views\data_scanner_view.py
import flet as ft
from src.Gui.FletGui2.views import ui_constants as C
from src.Gui.FletGui2.views.date_range_components import DateRangeComponent

def get_view(page: ft.Page, view_display_name: str, show_home_callback):
    """Returns the content for the Data Scanner view with tabbed interface."""

    def _handle_return_click(e):
        show_home_callback()

    # Header section
    header = ft.Container(
        content=ft.Column(
            [
                ft.Text(
                    f"{view_display_name} Interface",
                    size=24,
                    color=C.TEXT_COLOR,
                    weight=ft.FontWeight.BOLD,
                    font_family=C.FONT_ORBITRON
                ),
                ft.Text(
                    "Select date ranges for different test cases:",
                    color=C.TEXT_COLOR,
                    font_family=C.FONT_CONSOLAS
                ),
            ],
            spacing=8,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
        ),
        padding=ft.padding.only(bottom=20),
        alignment=ft.alignment.center
    )

    # Create tab content for each test case
    def create_test_case_tab(page: ft.Page, test_name: str, button_text: str = "Execute"):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    DateRangeComponent(page, test_name, button_text),
                ],
                expand=True,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )

    # Create tabs with improved styling
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="Basic Test",
                icon=ft.icons.DATA_ARRAY_ROUNDED,
                content=create_test_case_tab(page, "Basic Date Range Test")
            ),
            ft.Tab(
                text="Data Analysis",
                icon=ft.icons.ANALYTICS_ROUNDED,
                content=create_test_case_tab(page, "Data Analysis Test", "Analyze Data")
            ),
            ft.Tab(
                text="Report Generation",
                icon=ft.icons.INSERT_CHART_ROUNDED,
                content=create_test_case_tab(page, "Report Generation Test", "Generate Report")
            ),
            ft.Tab(
                text="Custom Test",
                icon=ft.icons.SETTINGS_ROUNDED,
                content=create_test_case_tab(page, "Custom Test Case", "Run Test")
            )
        ],
        expand=True,
        tab_alignment=ft.TabAlignment.CENTER,
        label_color=C.ACCENT_COLOR,
        unselected_label_color=ft.colors.with_opacity(0.7, C.TEXT_COLOR),
        indicator_color=C.ACCENT_COLOR,
        indicator_tab_size=True,
        label_padding=ft.padding.symmetric(horizontal=20, vertical=12),
        # Enhanced tab styling
        overlay_color=ft.colors.with_opacity(0.1, C.ACCENT_COLOR),
        divider_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
        indicator_border_radius=4,
        indicator_padding=ft.padding.symmetric(horizontal=8),
    )

    # The 'tabs' control is now directly included in the Column below,
    # replacing the 'tabs_container'.

    # Create a scrollable content area with improved layout
    scrollable_content = ft.Container(
        content=ft.Column(
            [
                header,
                # Tabs container with proper expansion
                ft.Container(
                    content=tabs,
                    expand=True,
                    margin=ft.margin.symmetric(vertical=10),
                ),
                # Return to home button at the bottom
                ft.Container(
                    ft.ElevatedButton(
                        "Return to Main Menu",
                        icon=ft.icons.ARROW_BACK_IOS_NEW_ROUNDED,
                        on_click=_handle_return_click,
                        style=ft.ButtonStyle(
                            color=ft.colors.WHITE,
                            bgcolor=C.ACCENT_COLOR,
                            padding=ft.padding.symmetric(horizontal=24, vertical=12),
                            shape=ft.RoundedRectangleBorder(radius=8),
                            elevation=3,
                            shadow_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
                        ),
                        height=48
                    ),
                    padding=ft.padding.only(top=20, bottom=16),
                    alignment=ft.alignment.center
                )
            ],
            spacing=0,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
        ),
        expand=True,
        padding=ft.padding.symmetric(horizontal=15, vertical=10)
    )
    
    # Main container with proper constraints
    return ft.Container(
        content=scrollable_content,
        expand=True,
        padding=ft.padding.only(top=10, bottom=10),
        margin=ft.margin.all(0)
    )
