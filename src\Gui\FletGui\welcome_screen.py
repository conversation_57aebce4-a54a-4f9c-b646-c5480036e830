import flet as ft
import random
import src.base.settings as setting

class WelcomeScreen:
    def __init__(self):
        self.page = None
        self.on_complete_callback = None
        self.animation_container = None
        self.progress_bar = None
        self.logo_container = None
        self.title_text = None
        self.subtitle_text = None
        self.particles = []
        self.circuit_lines = []
        self.loading_dots = []
        self.scan_line = None
        
    def create_particle(self, x, y):
        """创建粒子效果"""
        return ft.Container(
            width=6,
            height=6,
            bgcolor=ft.colors.CYAN_300,
            border_radius=3,
            left=x,
            top=y,
            opacity=0.8,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
            animate_position=ft.animation.Animation(2000, ft.AnimationCurve.EASE_OUT),
        )
    
    def create_circuit_line(self, start_x, start_y, end_x, end_y):
        """创建电路线条效果"""
        return ft.Container(
            width=abs(end_x - start_x) if abs(end_x - start_x) > 0 else 3,
            height=abs(end_y - start_y) if abs(end_y - start_y) > 0 else 3,
            bgcolor=ft.colors.CYAN_400,
            left=min(start_x, end_x),
            top=min(start_y, end_y),
            opacity=0,
            animate_opacity=ft.animation.Animation(1500, ft.AnimationCurve.EASE_IN_OUT),
        )
    
    def create_loading_dot(self):
        """创建加载点动画"""
        return ft.Container(
            width=10,
            height=10,
            bgcolor=ft.colors.CYAN_400,
            border_radius=5,
            opacity=0.4,
            animate_opacity=ft.animation.Animation(800, ft.AnimationCurve.EASE_IN_OUT),
        )
    
    def create_welcome_ui(self):
        """创建欢迎界面UI"""
        # 创建粒子效果
        for _ in range(20):
            x = random.randint(50, 1150)
            y = random.randint(50, 680)
            particle = self.create_particle(x, y)
            self.particles.append(particle)

        # 创建电路线条
        for _ in range(8):
            start_x = random.randint(100, 500)
            start_y = random.randint(100, 300)
            end_x = start_x + random.randint(100, 300)
            end_y = start_y + random.randint(-50, 50)
            line = self.create_circuit_line(start_x, start_y, end_x, end_y)
            self.circuit_lines.append(line)

        # 创建加载点
        for _ in range(3):
            dot = self.create_loading_dot()
            self.loading_dots.append(dot)

        # 创建扫描线效果
        self.scan_line = ft.Container(
            width=1200,
            height=3,
            bgcolor=ft.colors.CYAN_300,
            opacity=0.7,
            top=0,
            animate_position=ft.animation.Animation(3000, ft.AnimationCurve.LINEAR),
        )
        
        # Logo容器 - 添加更多科技感效果
        self.logo_container = ft.Container(
            content=ft.Stack(
                controls=[
                    # 外圈发光效果
                    ft.Container(
                        width=220,
                        height=220,
                        border_radius=110,
                        border=ft.border.all(2, ft.colors.CYAN_300),
                        opacity=0.6,
                    ),
                    # 中圈
                    ft.Container(
                        width=200,
                        height=200,
                        border_radius=100,
                        border=ft.border.all(3, ft.colors.CYAN_400),
                        bgcolor=ft.colors.with_opacity(0.1, ft.colors.CYAN_400),
                    ),
                    # 内圈图标
                    ft.Container(
                        #ft.Image(src=setting.CACHE_PATH+"/rpa.ico",width=120,height=120),
                        #ft.Image(src=setting.CACHE_PATH+"/cscec3bit.png",width=150,height=150),
                        ft.Icon(ft.icons.WORK,size=120,color=ft.colors.CYAN_400,),
                        width=200,
                        height=200,
                        alignment=ft.alignment.center,
                    ),
                ],
                alignment=ft.alignment.center,
            ),
            width=220,
            height=220,
            alignment=ft.alignment.center,
            opacity=0,
            scale=ft.transform.Scale(0.5),
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
            animate_scale=ft.animation.Animation(1000, ft.AnimationCurve.BOUNCE_OUT),
        )
        
        # 标题文本
        self.title_text = ft.Text(
            "信小财 RPA 财务机器人",
            size=52,
            weight=ft.FontWeight.BOLD,
            color=ft.colors.WHITE,
            text_align=ft.TextAlign.CENTER,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
            style=ft.TextStyle(
                shadow=ft.BoxShadow(
                    spread_radius=2,
                    blur_radius=12,
                    color=ft.colors.CYAN_400,
                    offset=ft.Offset(0, 3),
                )
            ),
        )
        
        # 副标题文本
        self.subtitle_text = ft.Text(
            "企业隐形数字员工 •  安全可靠  •   成本效益高",
            #"智能化财务处理 • 高效自动化 • 精准可靠",
            size=22,
            color=ft.colors.CYAN_100,
            text_align=ft.TextAlign.CENTER,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
            style=ft.TextStyle(
                shadow=ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=6,
                    color=ft.colors.CYAN_600,
                    offset=ft.Offset(0, 2),
                )
            ),
        )
        
        # 进度条
        self.progress_bar = ft.ProgressBar(
            width=450,
            height=8,
            bgcolor=ft.colors.with_opacity(0.3, ft.colors.CYAN_200),
            color=ft.colors.CYAN_400,
            value=0,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
        )
        
        # 加载文本和点
        loading_row = ft.Row(
            controls=[
                ft.Text("正在启动", size=18, color=ft.colors.CYAN_100,
                       style=ft.TextStyle(
                           shadow=ft.BoxShadow(
                               spread_radius=1,
                               blur_radius=4,
                               color=ft.colors.CYAN_600,
                               offset=ft.Offset(0, 1),
                           )
                       )),
                *self.loading_dots,
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=8,
            opacity=0,
            animate_opacity=ft.animation.Animation(1000, ft.AnimationCurve.EASE_OUT),
        )
        
        # 主容器
        self.animation_container = ft.Stack(
            controls=[
                # 背景图片
                ft.Container(
                    width=1200,
                    height=730,
                    image_src=setting.CACHE_PATH+"/robot.png",
                    image_fit=ft.ImageFit.COVER,
                    image_opacity=0.85,
                ),
                # 半透明遮罩层，增强文字可读性
                ft.Container(
                    width=1200,
                    height=730,
                    bgcolor=ft.colors.with_opacity(0.3, ft.colors.BLACK),
                ),
                # 粒子效果
                *self.particles,
                # 电路线条
                *self.circuit_lines,
                # 扫描线
                self.scan_line,
                # 主要内容
                ft.Container(
                    content=ft.Column(
                        controls=[
                            ft.Container(height=100),  # 顶部间距
                            self.logo_container,
                            ft.Container(height=40),
                            self.title_text,
                            ft.Container(height=20),
                            self.subtitle_text,
                            ft.Container(height=80),
                            self.progress_bar,
                            ft.Container(height=20),
                            loading_row,
                        ],
                        alignment=ft.MainAxisAlignment.CENTER,
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    ),
                    width=1200,
                    height=730,
                    alignment=ft.alignment.center,
                ),
            ],
            width=1200,
            height=730,
        )
        
        return self.animation_container
    
    def animate_particles(self):
        """粒子动画效果"""
        import threading
        import time

        def particle_animation():
            for _ in range(10):  # 限制动画次数
                for particle in self.particles:
                    # 随机移动粒子
                    new_x = random.randint(50, 1150)
                    new_y = random.randint(50, 680)
                    particle.left = new_x
                    particle.top = new_y
                    particle.opacity = random.uniform(0.4, 0.8)

                if self.page:
                    self.page.update()
                time.sleep(2)

        thread = threading.Thread(target=particle_animation, daemon=True)
        thread.start()

    def animate_loading_dots(self):
        """加载点动画"""
        import threading
        import time

        def dots_animation():
            for _ in range(20):  # 限制动画次数
                for dot in self.loading_dots:
                    time.sleep(0.2)
                    dot.opacity = 1.0
                    if self.page:
                        self.page.update()
                    time.sleep(0.3)
                    dot.opacity = 0.3
                    if self.page:
                        self.page.update()

        thread = threading.Thread(target=dots_animation, daemon=True)
        thread.start()

    def animate_scan_line(self):
        """扫描线动画"""
        import threading
        import time

        def scan_animation():
            for _ in range(5):  # 重复5次扫描
                self.scan_line.top = 0
                if self.page:
                    self.page.update()
                time.sleep(0.1)
                self.scan_line.top = 730
                if self.page:
                    self.page.update()
                time.sleep(3)

        thread = threading.Thread(target=scan_animation, daemon=True)
        thread.start()
    
    def start_animations(self):
        """启动所有动画"""
        import threading
        import time

        def animation_sequence():
            # 延迟显示各个元素
            time.sleep(0.5)

            # Logo动画
            #self.logo_container.opacity = 1
            #self.logo_container.scale = ft.transform.Scale(1.0)
            if self.page:
                self.page.update()

            time.sleep(0.8)

            # 标题动画
            self.title_text.opacity = 1
            if self.page:
                self.page.update()

            time.sleep(0.5)

            # 副标题动画
            self.subtitle_text.opacity = 1
            if self.page:
                self.page.update()

            time.sleep(0.5)

            # 电路线条动画
            for line in self.circuit_lines:
                line.opacity = 0.4
            if self.page:
                self.page.update()

            time.sleep(0.5)

            # 进度条和加载文本
            self.progress_bar.opacity = 1
            self.loading_dots[0].parent.opacity = 1
            if self.page:
                self.page.update()

            # 进度条动画
            for i in range(101):
                self.progress_bar.value = i / 100
                if self.page:
                    self.page.update()
                time.sleep(0.01)

            time.sleep(0.3)

            # 完成动画，调用回调
            if self.on_complete_callback:
                 self.on_complete_callback()

        thread = threading.Thread(target=animation_sequence, daemon=True)
        thread.start()
    
    def show(self, page: ft.Page, on_complete=None):
        """显示欢迎界面"""
        self.page = page
        self.on_complete_callback = on_complete
        
        # 设置页面属性
        page.window.height = 730
        page.window.width = 1200
        page.theme_mode = ft.ThemeMode.LIGHT
        page.padding = 0
        page.spacing = 0
        
        # 添加欢迎界面
        welcome_ui = self.create_welcome_ui()
        page.add(welcome_ui)
        page.update()
        
        # 启动动画
        self.start_animations()
        self.animate_particles()
        self.animate_loading_dots()
        self.animate_scan_line()
