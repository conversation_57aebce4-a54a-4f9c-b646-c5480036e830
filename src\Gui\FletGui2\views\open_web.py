#打开127.0.0.1:8000
import webbrowser
import flet as ft
from src.Gui.FletGui2.views import ui_constants as C
def get_view(page: ft.Page, view_display_name: str, show_home_callback):
    """Returns the content for the Data Scanner view with tabbed interface."""


    # 要打开的网页URL
    url = "https://example.com"  # 替换为你想打开的网址

    try:
        # 使用默认浏览器打开网页
        webbrowser.open(url)
        print(f"已尝试使用默认浏览器打开: {url}")
        
        # 可选：如果需要指定浏览器（以Chrome为例）
        # chrome_path = "C:/Program Files/Google/Chrome/Application/chrome.exe %s"
        # webbrowser.get(chrome_path).open(url)
        
    except Exception as e:
        print(f"打开网页时出错: {e}")
    async def _handle_return_click(e):
        await show_home_callback()
    button=ft.Container(
                    ft.ElevatedButton(
                        "Return to Main Menu",
                        icon=ft.icons.ARROW_BACK_IOS_NEW_ROUNDED,
                        on_click=_handle_return_click,
                        style=ft.ButtonStyle(
                            color=ft.colors.WHITE,
                            bgcolor=C.ACCENT_COLOR,
                            padding=ft.padding.symmetric(horizontal=24, vertical=12),
                            shape=ft.RoundedRectangleBorder(radius=8),
                            elevation=3,
                            shadow_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
                        ),
                        height=48
                    ),
                    padding=ft.padding.only(top=20, bottom=16),
                    alignment=ft.alignment.center
                )
    return button
    