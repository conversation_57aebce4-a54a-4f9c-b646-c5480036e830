# d:\infoTech\fip-rpa-robot-oldscriptref\FletGui2\views\system_diagnostics_view.py
import flet as ft
from . import ui_constants as C

def get_view(page: ft.Page, view_display_name: str, show_home_callback):
    """Returns the content for the System Diagnostics view."""

    async def _handle_return_click(e):
        await show_home_callback()

    content = ft.Column(
        [
            ft.Text(f"{view_display_name} Interface", size=24, color=C.TEXT_COLOR, weight=ft.FontWeight.BOLD, font_family=C.FONT_ORBITRON),
            ft.Text("System Diagnostics specific controls and information will appear here.", color=C.TEXT_COLOR, font_family=C.FONT_CONSOLAS),
            ft.Divider(height=30, color=ft.colors.TRANSPARENT), # Spacer
            ft.ElevatedButton(
                "Return to Main Menu",
                icon=ft.icons.ARROW_BACK_IOS_NEW_ROUNDED,
                on_click=_handle_return_click,
                bgcolor=C.ACCENT_COLOR,
                color=C.PRIMARY_COLOR,
                style=ft.ButtonStyle(shape=ft.RoundedRectangleBorder(radius=5))
            )
        ],
        alignment=ft.MainAxisAlignment.START,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
        spacing=20,
        expand=True,
        scroll=ft.ScrollMode.ADAPTIVE
    )
    
    return ft.Container(
        content=content,
        padding=20,
        border_radius=10,
        expand=True
    )
